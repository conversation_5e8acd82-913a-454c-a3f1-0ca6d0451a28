{"name": "crm_react_expo", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@expo/websql": "^1.0.1", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/slider": "4.5.6", "@react-navigation/drawer": "^7.3.11", "@react-navigation/native": "^7.1.8", "@react-navigation/stack": "^7.3.1", "docxtemplater": "^3.61.2", "expo": "~53.0.9", "expo-background-fetch": "^13.1.5", "expo-dev-client": "~5.1.8", "expo-document-picker": "^13.1.5", "expo-file-system": "^18.1.10", "expo-linear-gradient": "^14.1.4", "expo-print": "^14.1.4", "expo-sharing": "^13.1.5", "expo-sqlite": "^15.2.10", "expo-status-bar": "~2.2.3", "expo-task-manager": "^13.1.5", "file-saver": "^2.0.5", "pizzip": "^3.1.8", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-pager-view": "^6.7.1", "react-native-paper": "^5.14.1", "react-native-reanimated": "^3.17.5", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-tab-view": "^4.0.12", "react-native-web": "^0.20.0", "victory-native": "^41.17.1", "expo-clipboard": "~7.1.4", "expo-intent-launcher": "~12.1.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/file-saver": "^2.0.7", "@types/react": "~19.0.10", "babel-plugin-transform-remove-console": "^6.9.4", "typescript": "~5.8.3"}, "private": true}