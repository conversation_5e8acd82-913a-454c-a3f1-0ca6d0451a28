{"expo": {"name": "SAMVIDA", "slug": "crm_react_expo", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "newArchEnabled": false, "splash": {"image": "./assets/samvida-logo.png", "resizeMode": "contain", "backgroundColor": "#4F46E5"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.crmapp.mobile", "infoPlist": {"UIBackgroundModes": ["background-fetch"]}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#4F46E5"}, "package": "com.crmapp.mobile", "edgeToEdgeEnabled": true, "permissions": ["android.permission.WAKE_LOCK", "android.permission.RECEIVE_BOOT_COMPLETED", "android.permission.FOREGROUND_SERVICE"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-sqlite", "expo-background-fetch", "expo-task-manager"], "extra": {"eas": {"projectId": "aef1ab9f-a2db-420d-b2d2-b4e8cc5c7b50"}}}}